# 小田螺助手应用测试指南

## 📱 应用更新说明

### ✨ 新增功能
1. **沉浸式全屏模式** - 提供更好的视觉体验
2. **改进的网络状态显示** - 实时显示连接状态和服务器地址
3. **重新连接功能** - 一键重新检查网络连接
4. **优化的UI界面** - 更美观的状态卡片和按钮

### 🔧 技术优化
1. **网络配置管理** - 支持动态配置服务器地址
2. **异常处理机制** - 更好的错误提示和重试机制
3. **数据模型标准化** - 统一的API数据格式
4. **依赖版本管理** - 解决版本冲突问题

## 🚀 安装和测试步骤

### 1. 安装新版本APK
```bash
# APK文件位置
conch-client/build/outputs/apk/debug/conch-client-debug.apk

# 安装命令（如果有ADB）
adb install -r conch-client-debug.apk
```

### 2. 启动服务端
```bash
# 设置Java环境
$env:JAVA_HOME="C:\Program Files\Android\Android Studio\jbr"

# 启动服务端
./gradlew :conch-server:bootRun
```

### 3. 验证服务端状态
```bash
# 检查健康状态
curl http://localhost:8080/api/v1/health

# 或使用PowerShell
(Invoke-WebRequest -Uri "http://localhost:8080/api/v1/health" -Method GET).Content
```

### 4. 应用功能测试

#### 网络连接测试
1. 打开应用
2. 查看顶部网络状态卡片
3. 如果显示"连接失败"，点击"重新连接"按钮
4. 确认显示"已连接"状态

#### 指令测试
1. 在输入框中输入测试指令：
   - "open gallery" 
   - "send message"
   - "open camera"
2. 或点击快捷按钮
3. 点击"执行指令"
4. 观察执行进度和日志

#### 沉浸式全屏测试
1. 应用启动后应自动进入全屏模式
2. 状态栏和导航栏应隐藏
3. 从屏幕边缘滑动可临时显示系统栏
4. 系统栏会自动隐藏

## 🔍 故障排除

### 网络连接失败
**可能原因：**
1. 服务端未启动
2. IP地址配置错误
3. 防火墙阻止连接
4. 网络不在同一局域网

**解决方案：**
1. 确认服务端在8080端口运行
2. 检查IP地址是否为 `***********`
3. 关闭防火墙或添加例外
4. 确保手机和电脑在同一WiFi网络

### 应用无法安装
**可能原因：**
1. 未开启"未知来源"安装
2. 旧版本冲突

**解决方案：**
1. 在设置中开启"允许安装未知来源应用"
2. 先卸载旧版本再安装新版本

### 指令执行失败
**可能原因：**
1. 网络连接问题
2. 服务端处理异常

**解决方案：**
1. 检查网络连接状态
2. 查看服务端日志输出
3. 重启服务端和应用

## 📊 测试验证清单

### ✅ 基础功能
- [ ] 应用正常启动
- [ ] 沉浸式全屏生效
- [ ] 网络状态正确显示
- [ ] 重新连接功能正常

### ✅ 网络通信
- [ ] 健康检查API正常
- [ ] 指令提交API正常
- [ ] 错误处理机制正常
- [ ] 重试机制生效

### ✅ 用户界面
- [ ] 界面布局正常
- [ ] 状态卡片显示正确
- [ ] 按钮响应正常
- [ ] 进度显示正常

### ✅ 异常处理
- [ ] 网络断开时显示错误
- [ ] 服务端异常时正确提示
- [ ] 重连功能正常工作
- [ ] 超时处理正确

## 🎯 预期结果

1. **应用启动** - 全屏模式，界面美观
2. **网络状态** - 显示"已连接"和正确的服务器地址
3. **指令执行** - 能够发送指令并接收响应
4. **日志显示** - 实时显示执行过程和结果
5. **错误处理** - 网络问题时有明确提示和重连选项

## 📝 测试报告模板

```
测试时间：____年__月__日
测试设备：________________
Android版本：_____________

基础功能测试：
□ 应用启动正常
□ 全屏模式生效
□ 网络连接正常
□ 界面显示正确

功能测试：
□ 指令提交成功
□ 响应接收正常
□ 进度显示正确
□ 日志输出正常

异常测试：
□ 网络断开处理
□ 服务端异常处理
□ 重连功能正常

问题记录：
1. ________________________
2. ________________________
3. ________________________

总体评价：□ 优秀 □ 良好 □ 一般 □ 需改进
```

## 🔧 开发者调试

如需调试，可以：
1. 查看Android Studio的Logcat输出
2. 检查服务端控制台日志
3. 使用网络抓包工具分析请求
4. 修改NetworkConfig中的默认IP地址

---

**注意：** 确保手机和电脑在同一网络环境下，且服务端正常运行在8080端口。
