apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'

android {
    namespace 'com.conch.client'
    compileSdk rootProject.ext.compileSdk
    defaultConfig {
        applicationId "com.conch.client"
        minSdk rootProject.ext.minSdk
        targetSdk rootProject.ext.targetSdk
        versionCode 1
        versionName "1.0.0"
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

        ndk {
            //noinspection ChromeOsAbiSupport
            abiFilters "armeabi-v7a", "arm64-v8a"
        }
    }

    buildTypes {
        debug {
            minifyEnabled false
            debuggable true
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
        kotlinOptions {
            jvmTarget = "17"
        }
    }
    
    buildFeatures {
        viewBinding true
        compose true
    }

    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.10"
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    // Android核心 - 使用版本目录
    implementation libs.bundles.android.core
    implementation libs.androidx.activity.compose

    // Compose UI - 使用版本目录
    implementation platform(libs.androidx.compose.bom)
    implementation libs.bundles.compose

    // Kotlin
    implementation libs.kotlin.stdlib

    // 网络 (如果需要的话)
    // implementation libs.bundles.network

    // 测试
    testImplementation libs.bundles.test
    androidTestImplementation libs.bundles.test
}
