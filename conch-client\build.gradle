apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'

android {
    namespace 'com.conch.client'
    compileSdk rootProject.ext.compileSdk
    defaultConfig {
        applicationId "com.conch.client"
        minSdk rootProject.ext.minSdk
        targetSdk rootProject.ext.targetSdk
        versionCode 1
        versionName "1.0.0"
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'

        ndk {
            //noinspection ChromeOsAbiSupport
            abiFilters "armeabi-v7a", "arm64-v8a"
        }
    }

    buildTypes {
        debug {
            minifyEnabled false
            debuggable true
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
        kotlinOptions {
            jvmTarget = "17"
        }
    }
    
    buildFeatures {
        viewBinding true
        compose true
    }

    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.10"
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    // Android核心 - 最小化
    implementation "androidx.core:core-ktx:1.9.0"
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:2.6.2"
    implementation "androidx.activity:activity-compose:1.7.2"
    implementation "androidx.appcompat:appcompat:${rootProject.ext.appcompatVersion}"
    implementation "com.google.android.material:material:${rootProject.ext.materialVersion}"

    // Compose UI - 基础版本
    implementation platform("androidx.compose:compose-bom:2023.08.00")
    implementation "androidx.compose.ui:ui"
    implementation "androidx.compose.ui:ui-tooling-preview"
    implementation "androidx.compose.material3:material3"

    // Kotlin
    implementation "org.jetbrains.kotlin:kotlin-stdlib:${rootProject.ext.kotlinGradleVersion}"

    // 测试
    testImplementation "junit:junit:4.13.2"
    androidTestImplementation "androidx.test.ext:junit:1.1.5"
    androidTestImplementation "androidx.test.espresso:espresso-core:3.5.1"
}
