apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'
apply plugin: 'kotlin'
apply plugin: 'kotlin-spring'

group = 'com.conch'
version = '1.0.0'
sourceCompatibility = '17'

repositories {
    maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
    maven { url "https://maven.aliyun.com/repository/public" }
    maven { url 'https://repo1.maven.org/maven2/' }
    maven { url 'https://jitpack.io' }
    mavenCentral()
    google()
}

dependencies {
    // Spring Boot
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-websocket'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    
    // Kotlin
    implementation 'org.jetbrains.kotlin:kotlin-reflect'
    implementation 'org.jetbrains.kotlin:kotlin-stdlib-jdk8'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-reactor'
    
    // JSON处理
    implementation 'com.fasterxml.jackson.module:jackson-module-kotlin'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'
    
    // 开发工具
    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    
    // 测试
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.jetbrains.kotlin:kotlin-test-junit5'
    testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile) {
    kotlinOptions {
        freeCompilerArgs = ['-Xjsr305=strict']
        jvmTarget = '17'
    }
}

tasks.named('test') {
    useJUnitPlatform()
}
