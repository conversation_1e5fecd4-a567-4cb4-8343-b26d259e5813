// Top-level build file where you can add configuration options common to all sub-projects/modules.


buildscript {
    // 使用版本目录管理依赖版本
    ext {
        // 保持向后兼容的版本变量
        compileSdk = libs.versions.compileSdk.get() as Integer
        minSdk = libs.versions.minSdk.get() as Integer
        targetSdk = libs.versions.targetSdk.get() as Integer
        versionCode = libs.versions.versionCode.get() as Integer
        versionName = libs.versions.versionName.get()
        mavenVersion = libs.versions.mavenVersion.get()

        // 常用版本变量（向后兼容）
        appcompatVersion = libs.versions.appcompat.get()
        materialVersion = libs.versions.material.get()
        kotlinGradleVersion = libs.versions.kotlin.get()
    }
    repositories {
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url "https://maven.aliyun.com/repository/public" }
        maven { url 'https://repo1.maven.org/maven2/' }
        maven { url 'https://jitpack.io' }
        mavenCentral()
        google()
    }
    dependencies {
        classpath libs.plugins.android.application.get().pluginId + ':' + libs.versions.gradle.get()
        classpath libs.plugins.kotlin.android.get().pluginId + ':' + libs.versions.kotlin.get()
        classpath libs.plugins.hilt.get().pluginId + ':' + libs.versions.hilt.get()
        classpath libs.plugins.spring.boot.get().pluginId + ':' + libs.versions.spring.boot.get()
        classpath libs.plugins.kotlin.spring.get().pluginId + ':' + libs.versions.kotlin.get()

        classpath 'com.github.dcendents:android-maven-gradle-plugin:2.1'
    }
}
plugins {
    id 'org.jetbrains.dokka' version '1.8.10'
}
allprojects {
    repositories {
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url "https://maven.aliyun.com/repository/public" }
        maven { url 'https://repo1.maven.org/maven2/' }
        maven { url 'https://jitpack.io' }
        mavenCentral()
        google()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
