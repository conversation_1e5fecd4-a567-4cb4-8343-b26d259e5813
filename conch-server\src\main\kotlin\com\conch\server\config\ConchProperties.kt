package com.conch.server.config

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.stereotype.Component

/**
 * 小田螺助手配置属性
 */
@Component
@ConfigurationProperties(prefix = "conch")
data class ConchProperties(
    var server: ServerConfig = ServerConfig(),
    var session: SessionConfig = SessionConfig(),
    var script: ScriptConfig = ScriptConfig(),
    var feedback: FeedbackConfig = FeedbackConfig()
)

/**
 * 服务端配置
 */
data class ServerConfig(
    var name: String = "小田螺助手服务端",
    var version: String = "1.0.0",
    var description: String = "智能手机自动化助手服务端"
)

/**
 * 会话配置
 */
data class SessionConfig(
    var timeout: Long = 300000,  // 5分钟
    var maxConcurrent: Int = 100,
    var cleanupInterval: Long = 60000  // 1分钟
)

/**
 * 脚本配置
 */
data class ScriptConfig(
    var maxActions: Int = 50,
    var defaultTimeout: Long = 5000,
    var maxRetryCount: Int = 3
)

/**
 * 反馈配置
 */
data class FeedbackConfig(
    var imageMaxSize: Long = 2097152,  // 2MB
    var retentionDays: Int = 30
)
