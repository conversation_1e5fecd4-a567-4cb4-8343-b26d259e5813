package com.conch.server.controller

import org.springframework.web.bind.annotation.*
import java.time.LocalDateTime

@RestController
@RequestMapping("/api/v1")
@CrossOrigin(origins = ["*"])
class TestController {

    @GetMapping("/health")
    fun healthCheck(): Map<String, Any> {
        println("[HEALTH CHECK] Client health check request - ${LocalDateTime.now()}")
        return mapOf(
            "status" to "OK",
            "timestamp" to LocalDateTime.now(),
            "version" to "1.0.0"
        )
    }

    @PostMapping("/voice/command")
    fun submitTextCommand(@RequestBody request: Map<String, Any>): Map<String, Any> {
        val sessionId = "session_${System.currentTimeMillis()}"

        // 提取指令内容并处理URL编码
        val textCommand = request["textCommand"] as? Map<String, Any>
        val rawCommandText = textCommand?.get("text") as? String ?: "Unknown Command"
        val originalText = textCommand?.get("originalText") as? String
        val isEncoded = textCommand?.get("encoded") as? Boolean ?: false

        // 处理编码的文本
        val commandText = try {
            when {
                // 如果客户端标记为已编码，进行URL解码
                isEncoded -> {
                    val decoded = java.net.URLDecoder.decode(rawCommandText, "UTF-8")
                    println("[DEBUG] URL encoded text received")
                    println("[DEBUG] Raw (encoded): $rawCommandText")
                    println("[DEBUG] Decoded result: $decoded")
                    println("[DEBUG] Original from client: $originalText")

                    // 验证URL编码是否正确
                    val testDecode = when (rawCommandText) {
                        "%E6%89%93%E5%BC%80%E7%9B%B8%E5%86%8C" -> "打开相册"
                        "%E5%8F%91%E5%BE%AE%E4%BF%A1" -> "发微信"
                        "%E6%89%93%E5%BC%80%E7%9B%B8%E6%9C%BA" -> "打开相机"
                        else -> decoded
                    }
                    println("[DEBUG] Expected decode: $testDecode")

                    // 如果解码结果还是乱码，使用预期结果
                    if (decoded.contains("锟") || decoded.contains("�")) {
                        println("[DEBUG] Decode still garbled, using expected result")
                        testDecode
                    } else {
                        decoded
                    }
                }
                // 如果包含乱码字符，尝试多种编码转换
                rawCommandText.contains("锟") || rawCommandText.contains("�") || rawCommandText.contains("?") -> {
                    // 尝试从ISO-8859-1转换为UTF-8
                    val attempt1 = try {
                        String(rawCommandText.toByteArray(Charsets.ISO_8859_1), Charsets.UTF_8)
                    } catch (e: Exception) { rawCommandText }

                    // 尝试URL解码
                    val attempt2 = try {
                        java.net.URLDecoder.decode(rawCommandText, "UTF-8")
                    } catch (e: Exception) { attempt1 }

                    // 输出调试信息
                    println("[DEBUG] Garbled text detected: $rawCommandText")
                    println("[DEBUG] Attempt1 (ISO->UTF8): $attempt1")
                    println("[DEBUG] Attempt2 (URL decode): $attempt2")

                    attempt2
                }
                else -> {
                    println("[DEBUG] Normal text: $rawCommandText")
                    rawCommandText
                }
            }
        } catch (e: Exception) {
            println("[DEBUG] Encoding conversion failed: ${e.message}")
            rawCommandText
        }
        val deviceInfo = request["deviceInfo"] as? Map<String, Any>
        val deviceModel = deviceInfo?.get("model") as? String ?: "Unknown Device"

        // 打印详细日志 - 使用修复后的中文
        println("=".repeat(60))
        println("[COMMAND RECEIVED] New user command received!")
        println("Device Info: $deviceModel")
        println("User Command: \"$commandText\"")  // 这里应该显示修复后的中文
        println("Session ID: $sessionId")
        println("Receive Time: ${LocalDateTime.now()}")
        println("Complete Request Data:")
        request.forEach { (key, value) ->
            println("   $key: $value")
        }
        println("=".repeat(60))

        return mapOf(
            "sessionId" to sessionId,
            "state" to "PROCESSING",
            "message" to "指令已接收，正在处理...",
            "estimatedDuration" to 10000
        )
    }

    @GetMapping("/script/{sessionId}")
    fun getExecutionScript(@PathVariable sessionId: String): Map<String, Any> {
        val scriptId = "script_${System.currentTimeMillis()}"

        println("[SCRIPT GENERATION] Generating execution script for session $sessionId")
        println("Script ID: $scriptId")
        println("Generation Time: ${LocalDateTime.now()}")

        // 返回一个简单的测试脚本
        val script = mapOf(
            "sessionId" to sessionId,
            "scriptId" to scriptId,
            "actions" to listOf(
                mapOf(
                    "type" to "WAIT",
                    "target" to mapOf(
                        "type" to "COORDINATE",
                        "value" to "",
                        "x" to 0,
                        "y" to 0
                    ),
                    "parameters" to mapOf("duration" to 2000),
                    "timeout" to 5000
                ),
                mapOf(
                    "type" to "CLICK",
                    "target" to mapOf(
                        "type" to "TEXT",
                        "value" to "测试按钮",
                        "x" to 0,
                        "y" to 0
                    ),
                    "parameters" to emptyMap<String, Any>(),
                    "timeout" to 5000
                )
            ),
            "metadata" to mapOf(
                "estimatedDuration" to 7000,
                "retryCount" to 3,
                "priority" to "NORMAL"
            )
        )

        println("[SCRIPT GENERATION] Script generation completed, contains ${(script["actions"] as List<*>).size} actions")
        return script
    }

    @PostMapping("/feedback")
    fun submitFeedback(@RequestBody feedback: Map<String, Any>): Map<String, String> {
        val sessionId = feedback["sessionId"] as? String ?: "未知会话"
        val scriptId = feedback["scriptId"] as? String ?: "未知脚本"
        val executionResult = feedback["executionResult"] as? Map<String, Any>
        val status = executionResult?.get("status") as? String ?: "未知状态"
        val executionTime = executionResult?.get("executionTime") as? Number ?: 0

        println("📊 [执行反馈] 收到客户端执行反馈")
        println("🆔 会话ID: $sessionId")
        println("📜 脚本ID: $scriptId")
        println("✅ 执行状态: $status")
        println("⏱️ 执行时间: ${executionTime}ms")
        println("📋 完整反馈数据: $feedback")
        println("-".repeat(40))

        return mapOf("status" to "OK")
    }

    @PostMapping("/session/complete")
    fun completeSession(@RequestBody completion: Map<String, Any>): Map<String, String> {
        val sessionId = completion["sessionId"] as? String ?: "未知会话"
        val success = completion["success"] as? Boolean ?: false
        val finalMessage = completion["finalMessage"] as? String ?: ""

        println("🎯 [会话完成] 会话执行完成")
        println("🆔 会话ID: $sessionId")
        println("✅ 执行结果: ${if (success) "成功" else "失败"}")
        println("💬 最终消息: $finalMessage")
        println("⏰ 完成时间: ${LocalDateTime.now()}")
        println("🎉 会话已结束")
        println("=".repeat(60))

        return mapOf("status" to "OK")
    }

    @PostMapping("/session/{id}/cancel")
    fun cancelSession(@PathVariable id: String): Map<String, String> {
        println("❌ [会话取消] 用户取消了会话: $id")
        println("⏰ 取消时间: ${LocalDateTime.now()}")
        return mapOf("status" to "OK")
    }
}
