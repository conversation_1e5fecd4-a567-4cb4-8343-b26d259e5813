package com.conch.client.network

/**
 * 网络异常类
 */
sealed class NetworkException(message: String, cause: Throwable? = null) : Exception(message, cause) {
    
    /**
     * 连接超时异常
     */
    class TimeoutException(message: String = "连接超时") : NetworkException(message)
    
    /**
     * 网络不可达异常
     */
    class NetworkUnavailableException(message: String = "网络不可用") : NetworkException(message)
    
    /**
     * 服务器错误异常
     */
    class ServerException(val code: Int, message: String) : NetworkException("服务器错误 ($code): $message")
    
    /**
     * 解析错误异常
     */
    class ParseException(message: String = "数据解析失败") : NetworkException(message)
    
    /**
     * 未知网络错误
     */
    class UnknownException(message: String = "未知网络错误", cause: Throwable? = null) : NetworkException(message, cause)
}

/**
 * 网络结果封装
 */
sealed class NetworkResult<out T> {
    data class Success<T>(val data: T) : NetworkResult<T>()
    data class Error(val exception: NetworkException) : NetworkResult<Nothing>()
    
    inline fun <R> map(transform: (T) -> R): NetworkResult<R> {
        return when (this) {
            is Success -> Success(transform(data))
            is Error -> this
        }
    }
    
    inline fun onSuccess(action: (T) -> Unit): NetworkResult<T> {
        if (this is Success) action(data)
        return this
    }
    
    inline fun onError(action: (NetworkException) -> Unit): NetworkResult<T> {
        if (this is Error) action(exception)
        return this
    }
}
