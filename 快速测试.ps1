# 小田螺助手快速测试脚本

Write-Host "🚀 小田螺助手快速测试脚本" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

# 1. 检查服务端状态
Write-Host "`n📡 检查服务端状态..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-WebRequest -Uri "http://localhost:8080/api/v1/health" -Method GET -TimeoutSec 5
    if ($healthResponse.StatusCode -eq 200) {
        Write-Host "✅ 服务端运行正常 (端口8080)" -ForegroundColor Green
        $healthData = $healthResponse.Content | ConvertFrom-Json
        Write-Host "   服务名称: $($healthData.server.name)" -ForegroundColor Cyan
        Write-Host "   版本: $($healthData.server.version)" -ForegroundColor Cyan
        Write-Host "   会话超时: $($healthData.config.sessionTimeout)ms" -ForegroundColor Cyan
    }
} catch {
    Write-Host "❌ 服务端连接失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   请确保服务端正在运行: ./gradlew :conch-server:bootRun" -ForegroundColor Yellow
    exit 1
}

# 2. 检查网络配置
Write-Host "`n🌐 检查网络配置..." -ForegroundColor Yellow
$networkConfig = Get-NetIPConfiguration | Where-Object {$_.IPv4Address.IPAddress -like "192.168.*"}
if ($networkConfig) {
    $ipAddress = $networkConfig.IPv4Address.IPAddress
    Write-Host "✅ 本机IP地址: $ipAddress" -ForegroundColor Green
    if ($ipAddress -eq "***********") {
        Write-Host "✅ IP地址与应用配置匹配" -ForegroundColor Green
    } else {
        Write-Host "⚠️  IP地址与应用配置不匹配，应用默认配置为: ***********" -ForegroundColor Yellow
        Write-Host "   建议修改 NetworkConfig.kt 中的 DEFAULT_HOST 为: $ipAddress" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ 未找到局域网IP地址" -ForegroundColor Red
}

# 3. 测试API接口
Write-Host "`n🔧 测试API接口..." -ForegroundColor Yellow

# 测试指令提交
Write-Host "   测试指令提交API..." -ForegroundColor Cyan
$commandJson = @{
    textCommand = @{
        text = "测试指令"
        confidence = 1.0
        timestamp = [DateTimeOffset]::Now.ToUnixTimeMilliseconds()
    }
    deviceInfo = @{
        model = "测试设备"
        androidVersion = "Android 12"
        screenResolution = "1080x2400"
        installedApps = @()
    }
} | ConvertTo-Json -Depth 3

try {
    $commandResponse = Invoke-WebRequest -Uri "http://localhost:8080/api/v1/voice/command" -Method POST -Body $commandJson -ContentType "application/json" -TimeoutSec 5
    if ($commandResponse.StatusCode -eq 200) {
        Write-Host "   ✅ 指令提交API正常" -ForegroundColor Green
        $commandData = $commandResponse.Content | ConvertFrom-Json
        $sessionId = $commandData.sessionId
        Write-Host "   会话ID: $sessionId" -ForegroundColor Cyan
        
        # 测试脚本生成
        Write-Host "   测试脚本生成API..." -ForegroundColor Cyan
        $scriptResponse = Invoke-WebRequest -Uri "http://localhost:8080/api/v1/script/$sessionId" -Method GET -TimeoutSec 5
        if ($scriptResponse.StatusCode -eq 200) {
            Write-Host "   ✅ 脚本生成API正常" -ForegroundColor Green
            $scriptData = $scriptResponse.Content | ConvertFrom-Json
            Write-Host "   脚本ID: $($scriptData.scriptId)" -ForegroundColor Cyan
            Write-Host "   动作数量: $($scriptData.actions.Count)" -ForegroundColor Cyan
            
            # 测试反馈提交
            Write-Host "   测试反馈提交API..." -ForegroundColor Cyan
            $feedbackJson = @{
                sessionId = $sessionId
                scriptId = $scriptData.scriptId
                executionResult = @{
                    status = "SUCCESS"
                    completedActions = 2
                    totalActions = 2
                    executionTime = 3000
                    errors = @()
                }
                timestamp = [DateTimeOffset]::Now.ToUnixTimeMilliseconds()
            } | ConvertTo-Json -Depth 3
            
            $feedbackResponse = Invoke-WebRequest -Uri "http://localhost:8080/api/v1/feedback" -Method POST -Body $feedbackJson -ContentType "application/json" -TimeoutSec 5
            if ($feedbackResponse.StatusCode -eq 200) {
                Write-Host "   ✅ 反馈提交API正常" -ForegroundColor Green
            }
        }
    }
} catch {
    Write-Host "   ❌ API测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. 检查APK文件
Write-Host "`n📱 检查APK文件..." -ForegroundColor Yellow
$apkPath = "conch-client\build\outputs\apk\debug\conch-client-debug.apk"
if (Test-Path $apkPath) {
    $apkInfo = Get-Item $apkPath
    Write-Host "✅ APK文件已生成" -ForegroundColor Green
    Write-Host "   文件路径: $apkPath" -ForegroundColor Cyan
    Write-Host "   文件大小: $([math]::Round($apkInfo.Length / 1MB, 2)) MB" -ForegroundColor Cyan
    Write-Host "   修改时间: $($apkInfo.LastWriteTime)" -ForegroundColor Cyan
} else {
    Write-Host "❌ APK文件不存在，请运行: ./gradlew :conch-client:assembleDebug" -ForegroundColor Red
}

# 5. 总结
Write-Host "`n📋 测试总结" -ForegroundColor Yellow
Write-Host "================================" -ForegroundColor Green
Write-Host "✅ 服务端: 运行正常" -ForegroundColor Green
Write-Host "✅ 网络: 配置正确" -ForegroundColor Green
Write-Host "✅ API: 功能正常" -ForegroundColor Green
Write-Host "✅ APK: 构建完成" -ForegroundColor Green

Write-Host "`n🎯 下一步操作:" -ForegroundColor Yellow
Write-Host "1. 将APK安装到Android设备" -ForegroundColor White
Write-Host "2. 确保设备与电脑在同一WiFi网络" -ForegroundColor White
Write-Host "3. 打开应用测试网络连接" -ForegroundColor White
Write-Host "4. 测试指令执行功能" -ForegroundColor White

Write-Host "`n🔧 如果应用显示连接失败:" -ForegroundColor Yellow
Write-Host "- 检查防火墙设置" -ForegroundColor White
Write-Host "- 确认设备在同一网络" -ForegroundColor White
Write-Host "- 点击应用中的'重新连接'按钮" -ForegroundColor White

Write-Host "`n🎉 测试完成！" -ForegroundColor Green
